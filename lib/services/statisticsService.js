const _ = require('lodash');
const moment = require('moment');
const StatisticsUtils = require('../utils/statisticsUtils');
const attendanceService = require('./attendanceService');
const CONSTANTS = require('../const');

// Models
const User = require('../models/user');
const DutyShift = require('../models/dutyShift');
const AttendanceRecord = require('../models/attendanceRecord');
const WorkSchedule = require('../models/workSchedule');
const LeaveRequest = require('../models/leaveRequest');
const Area = require('../models/area');
const Report = require('../models/report');
const ReportDetail = require('../models/reportDetail');

// Duty Schedule Models
const DutyMainSchedule = require('../models/dutyMainSchedule');
const DutySubSchedule = require('../models/dutySubSchedule');
const DutyLocationSchedule = require('../models/dutyLocationSchedule');
const DutyPatrolSchedule = require('../models/dutyPatrolSchedule');
const DutyStadiumSchedule = require('../models/dutyStadiumSchedule');
const DutyCriminalSchedule = require('../models/dutyCriminalSchedule');
const DutySpecializedSchedule = require('../models/dutySpecializedSchedule');

/**
 * Service xử lý logic thống kê cho hệ thống
 * Cung cấp các phương thức tính toán thống kê cho 4 API chính
 */
class StatisticsService {

  /**
   * Lấy danh sách cán bộ đang trực ban
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Danh sách cán bộ trực ban và thống kê
   */
  async getOnDutyOfficers(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      // Tính toán khoảng thời gian
      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);
      const currentTime = Date.now();

      // Lấy tất cả ca trực trong khoảng thời gian
      const dutyShifts = await this.getAllDutyShifts(period);

      // Lọc ra các ca trực đang diễn ra
      const onDutyShifts = dutyShifts.filter(shift => {
        return shift.startTime <= currentTime && shift.endTime >= currentTime && shift.status === 1;
      });

      // Populate thông tin cán bộ
      const populatedShifts = await this.populateOfficerInfo(onDutyShifts);

      // Tính toán thống kê
      const summary = this.calculateOnDutySummary(populatedShifts);

      // Nhóm cán bộ theo loại lịch trực
      const dutyTypeGroups = this.groupOfficersByDutyType(populatedShifts);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách cán bộ trực ban thành công'
        },
        data: {
          period,
          currentTime,
          officers: populatedShifts, // Giữ nguyên mảng officers như cũ
          summary, // Giữ nguyên summary như cũ
          dutyTypeGroups // Thêm mới: dữ liệu đã nhóm theo loại lịch trực
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê số cán bộ điểm danh
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ điểm danh
   */
  async getAttendanceStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu attendance và work schedule
      const [attendanceRecords, workSchedules, dutyShifts] = await Promise.all([
        this.getAttendanceRecords(period),
        this.getWorkSchedules(period),
        this.getAllDutyShifts(period)
      ]);

      // Tính toán thống kê
      const summary = this.calculateAttendanceSummary(attendanceRecords, workSchedules, dutyShifts);
      const byShift = this.calculateAttendanceByShift(attendanceRecords, workSchedules);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ điểm danh thành công'
        },
        data: {
          period,
          summary,
          byShift
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê tổng số lượng cán bộ
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê tổng số lượng cán bộ
   */
  async getOfficerSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy tất cả dữ liệu cần thiết
      const [
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      ] = await Promise.all([
        this.getAllAreas(),
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period),
        this.getApprovedLeaveRequests(period)
      ]);

      // Tính toán thống kê tổng quan
      const summary = this.calculateOfficerSummary(
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      );

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng số lượng cán bộ thành công'
        },
        data: {
          period,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê cán bộ theo khu vực
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ theo khu vực
   */
  async getOfficersByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const [
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      ] = await Promise.all([
        this.getAllAreas(),
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period)
      ]);

      // Tính toán thống kê theo khu vực
      const areaStats = await this.calculateOfficersByArea(
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      );

      const summary = this.calculateAreaSummary(areaStats);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ theo khu vực thành công'
        },
        data: {
          period,
          areas: areaStats,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê số lượng báo cáo theo khu vực
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê báo cáo theo khu vực
   */
  async getReportsByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);
      const chartType = 'heatmap';

      // Lấy dữ liệu
      const [
        areas,
        reports,
        allOfficers
      ] = await Promise.all([
        this.getAllAreas(),
        this.getReports(period, chartType),
        this.getAllOfficers()
      ]);

      // Tính toán thống kê báo cáo theo khu vực
      const areaStats = await this.calculateReportsByArea(areas, reports, allOfficers);
      const summary = this.calculateReportAreaSummary(areaStats);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê báo cáo theo khu vực thành công'
        },
        data: {
          period,
          areas: areaStats,
          summary
        }
      };

    } catch (error) {
      console.error('Error in getReportsByAreaStats:', error);
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: error.message || 'Đã xảy ra lỗi khi lấy thống kê báo cáo theo khu vực'
        }
      };
    }
  }

  /**
   * Lấy thống kê ReportDetail theo khu vực (performance optimized)
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê ReportDetail theo khu vực
   */
  async getReportDetailsByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Sử dụng aggregation pipeline để lấy thống kê trực tiếp từ ReportDetail
      const pipeline = [
        // Match ReportDetails trong khoảng thời gian
        {
          $match: {
            time: {
              $gte: period.start,
              $lte: period.end
            }
          }
        },
        // Lookup Report để lấy thông tin báo cáo
        {
          $lookup: {
            from: 'reports',
            localField: 'reportId',
            foreignField: '_id',
            as: 'report'
          }
        },
        // Unwind report
        {
          $unwind: '$report'
        },
        // Match reports không bị xóa
        {
          $match: {
            'report.deletedAt': { $exists: false }
          }
        },
        // Unwind areas
        {
          $unwind: '$areas'
        },
        // Group theo area
        {
          $group: {
            _id: '$areas',
            totalDetails: { $sum: 1 },
            uniqueReports: { $addToSet: '$reportId' },
            reportTypes: { $push: '$report.reportType' },
            workStatuses: { $push: '$report.workStatus' },
            jobTypes: { $push: '$report.jobType' }
          }
        },
        // Project final result
        {
          $project: {
            areaId: '$_id',
            totalDetails: 1,
            totalReports: { $size: '$uniqueReports' },
            byReportType: {
              quick: {
                $size: {
                  $filter: {
                    input: '$reportTypes',
                    cond: { $eq: ['$$this', 'quick'] }
                  }
                }
              },
              detail: {
                $size: {
                  $filter: {
                    input: '$reportTypes',
                    cond: { $eq: ['$$this', 'detail'] }
                  }
                }
              }
            },
            byWorkStatus: {
              completed: {
                $size: {
                  $filter: {
                    input: '$workStatuses',
                    cond: { $eq: ['$$this', 'completed'] }
                  }
                }
              },
              in_progress: {
                $size: {
                  $filter: {
                    input: '$workStatuses',
                    cond: { $eq: ['$$this', 'in_progress'] }
                  }
                }
              },
              pending: {
                $size: {
                  $filter: {
                    input: '$workStatuses',
                    cond: { $eq: ['$$this', 'pending'] }
                  }
                }
              }
            }
          }
        },
        // Sort by totalDetails descending
        {
          $sort: { totalDetails: -1 }
        }
      ];

      const areaStats = await ReportDetail.aggregate(pipeline);

      // Populate area information
      const areaIds = areaStats.map(stat => stat.areaId);
      const areas = await Area.find({ _id: { $in: areaIds } }, 'name level').lean();

      const areasMap = areas.reduce((map, area) => {
        map[area._id.toString()] = area;
        return map;
      }, {});

      // Enrich area stats with area info
      const enrichedStats = areaStats.map(stat => ({
        ...stat,
        areaName: areasMap[stat.areaId.toString()]?.name || 'Unknown',
        areaLevel: areasMap[stat.areaId.toString()]?.level || 0
      }));

      // Calculate summary
      const summary = {
        totalAreas: enrichedStats.length,
        totalReports: enrichedStats.reduce((sum, stat) => sum + stat.totalReports, 0),
        totalDetails: enrichedStats.reduce((sum, stat) => sum + stat.totalDetails, 0),
        averageDetailsPerArea: StatisticsUtils.calculateRate(
          enrichedStats.reduce((sum, stat) => sum + stat.totalDetails, 0),
          enrichedStats.length
        ),
        averageReportsPerArea: StatisticsUtils.calculateRate(
          enrichedStats.reduce((sum, stat) => sum + stat.totalReports, 0),
          enrichedStats.length
        )
      };

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê ReportDetail theo khu vực thành công'
        },
        data: {
          period,
          areas: enrichedStats,
          summary
        }
      };

    } catch (error) {
      console.error('Error in getReportDetailsByAreaStats:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê tổng quan báo cáo
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê tổng quan báo cáo
   */
  async getReportsSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const reports = await this.getReports(period);

      // Tính toán thống kê tổng quan
      const summary = this.calculateReportsSummary(reports);
      const byStatus = this.calculateReportsByStatus(reports);
      const byReportType = this.calculateReportsByType(reports);
      const byJobType = this.calculateReportsByJobType(reports);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng quan báo cáo thành công'
        },
        data: {
          period,
          summary,
          byStatus,
          byReportType,
          byJobType
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê các vấn đề nổi bật (Incidents Highlight)
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê các vấn đề nổi bật
   */
  async getIncidentsHighlightStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId, area } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu báo cáo highlight trong khoảng thời gian hiện tại
      const currentReports = await this.getHighlightReports(period, area);

      // Tính toán thống kê theo lĩnh vực (jobtype) với color
      const byJobType = await this.calculateIncidentsByJobTypeWithColor(currentReports);

      // Tính toán thống kê theo khu vực (area)
      const byArea = await this.calculateIncidentsByArea(currentReports);

      // Tính toán thống kê theo ngày (day)
      const byDay = await this.calculateIncidentsByDay(area);

      // Tính toán tỷ lệ thay đổi so với khoảng thời gian trước (chỉ áp dụng cho non-custom timeRange)
      let changeRate = null;
      if (timeRange !== 'custom') {
        const previousPeriod = StatisticsUtils.getPreviousTimeRange(timeRange, period);
        const previousReports = await this.getHighlightReports(previousPeriod, area);
        changeRate = await this.calculateChangeRate(currentReports, previousReports, byJobType, byArea, true);
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê các vấn đề nổi bật thành công'
        },
        data: {
          period,
          summary: {
            totalIncidents: currentReports.reduce((total, report) => total + (report.details ? report.details.length : 0), 0),
            totalReports: currentReports.length
          },
          byJobType: changeRate ? changeRate.byJobType : byJobType,
          byArea: changeRate ? changeRate.byArea : byArea,
          byDay
          // changeRate
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê các vấn đề khác (Incidents Other)
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê các vấn đề khác
   */
  async getIncidentsOtherStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId, area } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu báo cáo other trong khoảng thời gian hiện tại
      const currentReports = await this.getOtherReports(period, area);

      // Tính toán thống kê theo lĩnh vực (jobtype)
      const byJobType = await this.calculateIncidentsByJobType(currentReports);

      // Tính toán thống kê theo khu vực (area)
      const byArea = await this.calculateIncidentsByArea(currentReports);

      // Tính toán tỷ lệ thay đổi so với khoảng thời gian trước (chỉ áp dụng cho non-custom timeRange)
      let changeRate = null;
      if (timeRange !== 'custom') {
        const previousPeriod = StatisticsUtils.getPreviousTimeRange(timeRange, period);
        const previousReports = await this.getOtherReports(previousPeriod, area);
        changeRate = await this.calculateChangeRate(currentReports, previousReports, byJobType, byArea);
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê các vấn đề khác thành công'
        },
        data: {
          period,
          summary: {
            totalIncidents: currentReports.reduce((total, report) => total + (report.details ? report.details.length : 0), 0),
            totalReports: currentReports.length
          },
          byJobType: changeRate ? changeRate.byJobType : byJobType,
          // byArea,
          // changeRate
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê báo cáo theo trạng thái chi tiết
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê báo cáo theo trạng thái
   */
  async getReportsStatusStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const reports = await this.getReports(period);

      // Tính toán thống kê theo trạng thái
      const statusStats = this.calculateDetailedStatusStats(reports);
      const workflowStats = this.calculateWorkflowStats(reports);
      const processingTime = this.calculateProcessingTimeStats(reports);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê báo cáo theo trạng thái thành công'
        },
        data: {
          period,
          statusStats,
          workflowStats,
          processingTime
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy 3 vụ việc mới nhất (Latest Incidents) - UPDATED: sử dụng ReportDetail thay vì metrics
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.area - ID khu vực để filter (optional)
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Danh sách 3 vụ việc trong 24 giờ gần nhất, sắp xếp mới nhất trước
   */
  async getLatestIncidents(params) {
    try {
      const { area, userId } = params;
      const MAX_INCIDENTS = 3;
      const TWENTY_FOUR_HOURS_AGO = Date.now() - 24 * 60 * 60 * 1000;

      // Lấy danh sách JobType có chartTypes chứa "highlight"
      const JobType = require('../models/jobType');
      const highlightJobTypes = await JobType.find({
        'quickReportTemplate.chartTypes': 'highlight',
        status: 1,
        deletedAt: { $exists: false }
      })
        .select('_id name quickReportTemplate')
        .lean();

      if (!highlightJobTypes || highlightJobTypes.length === 0) {
        return {
          success: true,
          message: {
            head: 'Thành công',
            body: 'Lấy danh sách vụ việc mới nhất thành công'
          },
          data: {
            incidents: [],
            total: 0,
            maxLimit: MAX_INCIDENTS,
            filter: {
              area: area || null,
              chartType: 'highlight'
            },
            generatedAt: Date.now(),
            message: 'Không có loại công việc nào trong vòng 24h'
          }
        };
      }

      // Lấy tất cả báo cáo có jobType highlight
      const highlightJobTypeIds = highlightJobTypes.map(jt => jt._id);
      const Report = require('../models/report');
      const reportQuery = {
        jobType: { $in: highlightJobTypeIds },
        deletedAt: { $exists: false },
        status: { $ne: 'draft' }
      };

      const highlightReports = await Report.find(reportQuery)
        .select('_id jobType title createdAt updatedAt')
        .populate({
          path: 'jobType',
          select: 'name description quickReportTemplate'
        })
        .lean();

      if (!highlightReports || highlightReports.length === 0) {
        return {
          success: true,
          message: {
            head: 'Thành công',
            body: 'Lấy danh sách vụ việc mới nhất thành công'
          },
          data: {
            incidents: [],
            total: 0,
            maxLimit: MAX_INCIDENTS,
            filter: {
              area: area || null,
              chartType: 'highlight'
            },
            generatedAt: Date.now(),
            message: 'Không có báo cáo highlight nào'
          }
        };
      }

      const reportIds = highlightReports.map(r => r._id);

      // Query ReportDetail với filter area
      let reportDetailQuery = {
        reportId: { $in: reportIds },
        time: { $gte: TWENTY_FOUR_HOURS_AGO }
      };

      // Thêm filter theo khu vực nếu có
      if (area) {
        try {
          const mongoose = require('mongoose');
          const areaObjectId = mongoose.Types.ObjectId(area);
          reportDetailQuery['areas'] = areaObjectId;
        } catch (err) {
          console.warn('Invalid area ObjectId:', area);
        }
      }

      // Lấy ReportDetail và populate areas
      const reportDetails = await ReportDetail.find(reportDetailQuery)
        .populate('areas', 'name level')
        .sort({ time: -1 }) // Sắp xếp theo thời gian mới nhất
        .limit(MAX_INCIDENTS)
        .lean();

      // Tạo map reports để lookup nhanh
      const reportsMap = highlightReports.reduce((map, report) => {
        map[report._id.toString()] = report;
        return map;
      }, {});

      // Tạo incidents từ ReportDetail
      const incidents = reportDetails.map(detail => {
        const report = reportsMap[detail.reportId.toString()];
        return this.createIncidentFromReportDetail(detail, report, area);
      });

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách vụ việc mới nhất thành công'
        },
        data: {
          incidents: incidents,
          total: incidents.length,
          maxLimit: MAX_INCIDENTS,
          filter: {
            area: area || null,
            chartType: 'highlight'
          },
          generatedAt: Date.now()
        }
      };

    } catch (error) {
      console.error('Error in getLatestIncidents:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê tổng quan văn bản
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'year', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê tổng quan văn bản
   */
  async getDocumentsSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu văn bản trong khoảng thời gian
      const documents = await this.getDocuments(period);

      // Lấy báo cáo công văn mới nhất để lấy số công văn chưa trả lời
      const latestDocumentReport = await this.getLatestDocumentReport();
      const unansweredDocuments = latestDocumentReport?.metrics?.unansweredDocuments || 0;

      // Tính toán thống kê
      const summary = this.calculateDocumentsSummary(documents, unansweredDocuments);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng quan văn bản thành công'
        },
        data: {
          period,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Lấy tất cả ca trực từ 8 loại lịch trực
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách tất cả ca trực
   */
  async getAllDutyShifts(period) {
    const dutyShifts = await DutyShift.find({
      $or: [
        { startTime: { $gte: period.startTimestamp, $lte: period.endTimestamp } },
        { endTime: { $gte: period.startTimestamp, $lte: period.endTimestamp } }
      ],
      status: { $ne: 2 } // Không lấy ca đã hủy
    })
      .populate('officer', 'name avatar idNumber units positions areas')
      .populate('unit', 'name')
      .lean();

    return dutyShifts;
  }

  /**
   * Populate thông tin chi tiết cán bộ cho ca trực
   * @param {Array} dutyShifts - Danh sách ca trực
   * @returns {Array} Ca trực đã được populate
   */
  async populateOfficerInfo(dutyShifts) {
    return dutyShifts.map(shift => ({
      userId: shift.officer._id,
      name: shift.officer.name,
      avatar: shift.officer.avatar,
      idNumber: shift.officer.idNumber,
      unit: shift.unit,
      positions: shift.officer.positions,
      dutyInfo: {
        dutyType: this.getDutyType(shift),
        dutyName: shift.name,
        location: shift.locationDuty,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status,
        forLeader: shift.forLeader,
        hasEquipment: shift.hasEquipment
      }
    }));
  }

  /**
   * Xác định loại trực từ ca trực
   * @param {Object} shift - Thông tin ca trực
   * @returns {String} Loại trực
   */
  getDutyType(shift) {
    // Logic xác định loại trực dựa trên các field reference
    if (shift.dutyMainSchedule) return 'main';
    if (shift.dutySubSchedule) return 'sub';
    if (shift.dutySpecializedSchedule) return 'specialized';
    if (shift.dutyCriminalSchedule) return 'criminal';
    if (shift.dutyPatrolSchedule) return 'patrol';
    if (shift.dutyLocationSchedule) return 'location';
    if (shift.dutyEmergencySchedule) return 'emergency';
    if (shift.dutyStadiumSchedule) return 'stadium';

    // Fallback logic cho ca trực tạo thủ công
    if (shift.source) {
      return shift.source;
    }

    if (shift.locationDuty) return 'location';
    if (shift.forLeader) return 'main';
    return 'manual';
  }

  /**
   * Tính toán thống kê tổng quan cho cán bộ trực ban
   * @param {Array} onDutyShifts - Danh sách ca trực đang diễn ra
   * @returns {Object} Thống kê tổng quan
   */
  calculateOnDutySummary(onDutyShifts) {
    const totalOnDuty = onDutyShifts.length;

    const byDutyType = _.countBy(onDutyShifts, 'dutyInfo.dutyType');
    const byStatus = _.countBy(onDutyShifts, 'dutyInfo.status');

    return {
      totalOnDuty,
      byDutyType,
      byStatus: {
        confirmed: byStatus[1] || 0,
        pending: byStatus[0] || 0,
        cancelled: byStatus[2] || 0
      }
    };
  }

  /**
   * Mapping thứ tự ưu tiên và tên hiển thị cho các loại lịch trực
   */
  getDutyTypeConfig() {
    return {
      'main': {
        priority: 1,
        name: 'Lịch thường trực chiến đấu tại trụ sở chính'
      },
      'sub': {
        priority: 2,
        name: 'Lịch thường trực chiến đấu tại trụ sở phụ'
      },
      'specialized': {
        priority: 3,
        name: 'Lịch trực ban chuyên trách'
      },
      'criminal': {
        priority: 4,
        name: 'Lịch trực ban hình sự'
      },
      'patrol': {
        priority: 5,
        name: 'Lịch tuần tra'
      },
      'location': {
        priority: 6,
        name: 'Lịch chốt điểm'
      },
      'emergency': {
        priority: 7,
        name: 'Lịch đột xuất'
      },
      'stadium': {
        priority: 8,
        name: 'Lịch sân bóng'
      },
      'manual': {
        priority: 9,
        name: 'Lịch trực khác'
      }
    };
  }

  /**
   * Nhóm cán bộ trực ban theo loại lịch trực và sắp xếp theo thứ tự ưu tiên
   * @param {Array} populatedShifts - Danh sách cán bộ trực ban đã được populate
   * @returns {Array} Danh sách nhóm theo loại lịch trực
   */
  groupOfficersByDutyType(populatedShifts) {
    const dutyTypeConfig = this.getDutyTypeConfig();

    // Nhóm cán bộ theo loại lịch trực
    const groupedByType = _.groupBy(populatedShifts, 'dutyInfo.dutyType');

    // Tạo danh sách kết quả với thứ tự ưu tiên
    const dutyTypeGroups = [];

    // Duyệt qua tất cả loại lịch trực theo thứ tự ưu tiên
    Object.keys(dutyTypeConfig)
      .sort((a, b) => dutyTypeConfig[a].priority - dutyTypeConfig[b].priority)
      .forEach(dutyType => {
        const officers = groupedByType[dutyType] || [];

        if (officers.length > 0) {
          // Tính khoảng thời gian sớm nhất - muộn nhất trong nhóm
          const startTimes = officers
            .map(o => o?.dutyInfo?.startTime)
            .filter(t => typeof t === 'number' && !isNaN(t));
          const endTimes = officers
            .map(o => o?.dutyInfo?.endTime)
            .filter(t => typeof t === 'number' && !isNaN(t));

          let timeRange = null;
          if (startTimes.length > 0 && endTimes.length > 0) {
            const earliestStart = Math.min(...startTimes);
            const latestEnd = Math.max(...endTimes);
            const startStr = moment(earliestStart).format('HH:mm');
            const endStr = moment(latestEnd).format('HH:mm');
            timeRange = `${startStr} - ${endStr}`;
          }

          dutyTypeGroups.push({
            dutyType: dutyType,
            dutyTypeName: dutyTypeConfig[dutyType].name,
            priority: dutyTypeConfig[dutyType].priority,
            officers: officers,
            summary: this.calculateDutyTypeSummary(officers),
            timeRange
          });
        }
      });

    return dutyTypeGroups;
  }

  /**
   * Tính toán thống kê cho một nhóm loại lịch trực
   * @param {Array} officers - Danh sách cán bộ trong nhóm
   * @returns {Object} Thống kê nhóm
   */
  calculateDutyTypeSummary(officers) {
    const totalOfficers = officers.length;
    const byStatus = _.countBy(officers, 'dutyInfo.status');

    return {
      totalOfficers,
      byStatus: {
        confirmed: byStatus[1] || 0,
        pending: byStatus[0] || 0,
        cancelled: byStatus[2] || 0
      }
    };
  }

  /**
   * Lấy dữ liệu attendance records
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách attendance records
   */
  async getAttendanceRecords(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await AttendanceRecord.find(dateQuery)
      .populate('user', 'name idNumber units positions')
      .lean();
  }

  /**
   * Lấy dữ liệu work schedules
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách work schedules
   */
  async getWorkSchedules(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await WorkSchedule.find({
      ...dateQuery,
      status: 1
    })
      .populate('user', 'name idNumber units positions')
      .lean();
  }

  /**
   * Lấy tất cả cán bộ
   * @returns {Array} Danh sách cán bộ
   */
  async getAllOfficers() {
    // Tạm thời không populate để tránh lỗi schema
    // Chỉ lấy dữ liệu cơ bản cần thiết cho tính toán
    return await User.find({ status: 1 })
      .select('name idNumber areas') // Chỉ lấy các field cần thiết
      .lean();
  }

  /**
   * Lấy tất cả khu vực
   * @returns {Array} Danh sách khu vực
   */
  async getAllAreas() {
    return await Area.find({ status: 1, level: 1 }) // Chỉ lấy level 1
      .lean();
  }

  /**
   * Lấy đơn xin nghỉ đã được duyệt
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách đơn xin nghỉ
   */
  async getApprovedLeaveRequests(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('startDate', period, 'ddmmyyyy');

    return await LeaveRequest.find({
      ...dateQuery,
      status: 'approved'
    })
      .populate('user', 'name idNumber')
      .lean();
  }

  /**
   * Lấy tất cả báo cáo trong khoảng thời gian với populate details từ ReportDetail (TEMP - using old logic for testing)
   * @param {Object} period - Khoảng thời gian
   * @param {String} chartType - Loại chart: 'pie', 'bar', 'line', 'heatmap'
   * @returns {Array} Danh sách báo cáo với details populated từ ReportDetail
   */
  async getReports(period, chartType = '') {
    // Temporary: Use old logic for testing
    const query = {
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    // Nếu có chartType, cần lọc qua JobType trước
    if (chartType) {
      const JobType = require('../models/jobType');

      // Tìm các JobType có chartType tương ứng
      const jobTypesWithChart = await JobType.find({
        'quickReportTemplate.chartTypes': chartType,
        status: 1,
        deletedAt: { $exists: false }
      }).select('_id').lean();

      if (jobTypesWithChart.length === 0) {
        return []; // Không có JobType nào hỗ trợ chartType này
      }

      // Thêm điều kiện lọc jobType
      query.jobType = { $in: jobTypesWithChart.map(jt => jt._id) };
    }

    // Lấy reports và populate details từ ReportDetail collection
    const reports = await Report.find(query)
      .populate('jobType', 'name quickReportTemplate')
      .lean();

    // Populate details từ ReportDetail collection
    const reportIds = reports.map(r => r._id);
    const reportDetails = await ReportDetail.find({
      reportId: { $in: reportIds }
    })
      .populate('areas', 'name level')
      .lean();

    // Group details theo reportId và filter areas level = 1
    const detailsMap = reportDetails.reduce((map, detail) => {
      if (!map[detail.reportId]) {
        map[detail.reportId] = [];
      }

      // Filter chỉ lấy areas có level = 1
      const level1Areas = detail.areas ? detail.areas.filter(area => area.level === 1) : [];

      map[detail.reportId].push({
        location: {
          areas: level1Areas,
          coordinates: detail.location?.coordinates,
          address: detail.location?.address,
          lat: detail.location?.lat,
          lng: detail.location?.lng
        },
        time: detail.time,
        createdAt: detail.createdAt,
        updatedAt: detail.updatedAt
      });
      return map;
    }, {});

    // Gán details vào reports
    const result = reports.map(report => ({
      ...report,
      details: detailsMap[report._id.toString()] || []
    }));

    return result;
  }

  /**
   * Lấy báo cáo highlight trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @param {String} area - ID khu vực để filter (optional)
   * @returns {Array} Danh sách báo cáo highlight
   */
  async getHighlightReports(period, area = null) {
    const JobType = require('../models/jobType');

    // Lấy tất cả JobType có chartTypes chứa 'highlight'
    const highlightJobTypes = await JobType.find({
      'quickReportTemplate.chartTypes': 'highlight',
      status: 1,
      deletedAt: { $exists: false }
    }).select('_id').lean();

    const highlightJobTypeIds = highlightJobTypes.map(jt => jt._id);

    if (highlightJobTypeIds.length === 0) {
      return [];
    }

    let reportIds = [];

    // Nếu có filter theo area, sử dụng ReportDetail để tìm reportIds
    if (area) {
      try {
        const mongoose = require('mongoose');
        const areaObjectId = mongoose.Types.ObjectId(area);

        // Tìm reportIds từ ReportDetail có area tương ứng
        const reportDetails = await ReportDetail.find({
          areas: areaObjectId,
          time: {
            $gte: period.startTimestamp,
            $lte: period.endTimestamp
          }
        }).select('reportId').lean();

        reportIds = reportDetails.map(rd => rd.reportId);

        if (reportIds.length === 0) {
          return []; // Không có report nào trong area này
        }
      } catch (err) {
        console.warn('Invalid area ObjectId:', area);
        // Nếu area ID không hợp lệ, bỏ qua filter
      }
    }

    const query = {
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      jobType: { $in: highlightJobTypeIds },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    // Thêm filter theo reportIds nếu có area filter
    if (area) {
      if (reportIds.length > 0) {
        query._id = { $in: reportIds };
      } else {
        // Nếu có area filter nhưng không có reportIds, nghĩa là không có report nào trong area này
        return [];
      }
    }

    const reports = await Report.find(query)
      .populate({
        path: 'createdBy',
        select: 'name idNumber units areas',
        populate: [
          {
            path: 'units',
            select: 'name'
          },
          {
            path: 'areas',
            select: 'name level'
          }
        ]
      })
      .populate('jobType', 'name quickReportTemplate')
      .lean();

    // Populate details từ ReportDetail với filter theo area nếu có
    const finalReportIds = reports.map(r => r._id);
    let reportDetailQuery = {
      reportId: { $in: finalReportIds }
    };

    // Nếu có filter theo area, chỉ lấy reportDetails thuộc area đó
    if (area) {
      try {
        const mongoose = require('mongoose');
        const areaObjectId = mongoose.Types.ObjectId(area);
        reportDetailQuery.areas = areaObjectId;
      } catch (err) {
        console.warn('Invalid area ObjectId in reportDetails filter:', area);
      }
    }

    const reportDetails = await ReportDetail.find(reportDetailQuery)
      .populate('areas', 'name level')
      .lean();

    // Group details theo reportId
    const detailsMap = reportDetails.reduce((map, detail) => {
      if (!map[detail.reportId]) {
        map[detail.reportId] = [];
      }
      map[detail.reportId].push({
        location: {
          areas: detail.areas,
          coordinates: detail.coordinates,
          address: detail.address
        },
        time: detail.time,
        description: detail.description
      });
      return map;
    }, {});

    // Gán details vào reports
    return reports.map(report => ({
      ...report,
      details: detailsMap[report._id.toString()] || []
    }));
  }

  /**
   * Lấy báo cáo khác trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @param {String} area - ID khu vực để filter (optional)
   * @returns {Array} Danh sách báo cáo khác
   */
  async getOtherReports(period, area = null) {
    const JobType = require('../models/jobType');

    // Lấy tất cả JobType có chartTypes khác 'highlight'
    const otherJobTypes = await JobType.find({
      // 'quickReportTemplate.chartTypes': { $ne: 'highlight' },
      'quickReportTemplate.chartTypes': 'heatmap',
      status: 1,
      deletedAt: { $exists: false }
    }).select('_id').lean();

    const otherJobTypeIds = otherJobTypes.map(jt => jt._id);

    if (otherJobTypeIds.length === 0) {
      return [];
    }

    let reportIds = [];

    // Nếu có filter theo area, sử dụng ReportDetail để tìm reportIds
    if (area) {
      try {
        const mongoose = require('mongoose');
        const areaObjectId = mongoose.Types.ObjectId(area);

        // Tìm reportIds từ ReportDetail có area tương ứng
        const reportDetails = await ReportDetail.find({
          areas: areaObjectId,
          time: {
            $gte: period.startTimestamp,
            $lte: period.endTimestamp
          }
        }).select('reportId').lean();

        reportIds = reportDetails.map(rd => rd.reportId);

        if (reportIds.length === 0) {
          return []; // Không có report nào trong area này
        }
      } catch (err) {
        console.warn('Invalid area ObjectId:', area);
        // Nếu area ID không hợp lệ, bỏ qua filter
      }
    }

    const query = {
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      jobType: { $in: otherJobTypeIds },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    // Thêm filter theo reportIds nếu có area filter
    if (area) {
      if (reportIds.length > 0) {
        query._id = { $in: reportIds };
      } else {
        // Nếu có area filter nhưng không có reportIds, nghĩa là không có report nào trong area này
        return [];
      }
    }

    const reports = await Report.find(query)
      .populate({
        path: 'createdBy',
        select: 'name idNumber units areas',
        populate: [
          {
            path: 'units',
            select: 'name'
          },
          {
            path: 'areas',
            select: 'name level'
          }
        ]
      })
      .populate('jobType', 'name quickReportTemplate')
      .lean();

    // Populate details từ ReportDetail với filter theo area nếu có
    const finalReportIds = reports.map(r => r._id);
    let reportDetailQuery = {
      reportId: { $in: finalReportIds }
    };

    // Nếu có filter theo area, chỉ lấy reportDetails thuộc area đó
    if (area) {
      try {
        const mongoose = require('mongoose');
        const areaObjectId = mongoose.Types.ObjectId(area);
        reportDetailQuery.areas = areaObjectId;
      } catch (err) {
        console.warn('Invalid area ObjectId in reportDetails filter:', area);
      }
    }

    const reportDetails = await ReportDetail.find(reportDetailQuery)
      .populate('areas', 'name level')
      .lean();

    // Group details theo reportId
    const detailsMap = reportDetails.reduce((map, detail) => {
      if (!map[detail.reportId]) {
        map[detail.reportId] = [];
      }
      map[detail.reportId].push({
        location: {
          areas: detail.areas,
          coordinates: detail.coordinates,
          address: detail.address
        },
        time: detail.time,
        description: detail.description
      });
      return map;
    }, {});

    // Gán details vào reports
    return reports.map(report => ({
      ...report,
      details: detailsMap[report._id.toString()] || []
    }));
  }

  /**
   * Tính toán thống kê cán bộ điểm danh tổng quan
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @param {Array} workSchedules - Danh sách work schedules
   * @param {Array} dutyShifts - Danh sách duty shifts
   * @returns {Object} Thống kê tổng quan
   */
  calculateAttendanceSummary(attendanceRecords, workSchedules, dutyShifts) {
    // Tính tổng số ca được lên lịch
    const totalScheduled = _.sumBy(workSchedules, schedule => schedule.shifts.length);

    // Đếm số cán bộ đã điểm danh
    const totalCheckedIn = attendanceRecords.length;

    // Đếm số cán bộ điểm danh muộn
    const totalLate = attendanceRecords.filter(record => record.status === 'late').length;

    // Đếm số cán bộ đúng giờ
    const totalOnTime = attendanceRecords.filter(record => record.status === 'on_time').length;

    // Đếm số cán bộ vắng mặt, excused, business_trip và nonattendance theo ca
    let totalAbsent = 0;
    let totalExcused = 0;
    let totalBusinessTrip = 0;
    let totalNonAttendance = 0;

    const attendanceService = require('./attendanceService');

    workSchedules.forEach(schedule => {
      schedule.shifts.forEach(shift => {
        // Kiểm tra xem có attendance record cho shift này không
        const hasAttendance = attendanceRecords.some(record =>
          record.user._id.toString() === schedule.user._id.toString() &&
          record.date === schedule.date &&
          record.shift === shift.type
        );

        if (!hasAttendance) {
          // Sử dụng logic từ attendanceService để xác định trạng thái chính xác
          const shiftStatus = attendanceService.getShiftStatus(shift, null, schedule.date);

          if (shiftStatus === 'absent') {
            totalAbsent++;
          } else if (shiftStatus === 'excused') {
            totalExcused++;
          } else if (shiftStatus === 'business_trip') {
            totalBusinessTrip++;
          } else if (shiftStatus === 'nonattendance') {
            totalNonAttendance++;
          } else {
            // Fallback cho các trạng thái khác
            if (shift.status === 'absent') {
              totalAbsent++;
            } else if (shift.status === 'excused') {
              totalExcused++;
            } else if (shift.status === 'business_trip') {
              totalBusinessTrip++;
            } else {
              // Mặc định là nonattendance nếu không có attendance record
              totalNonAttendance++;
            }
          }
        }
      });
    });

    // Đếm số cán bộ đang trực ban theo shiftsByOfficer và thời gian hiện tại
    const totalOnDuty = this.calculateTotalOfficersOnDuty(dutyShifts);

    // Tính tỷ lệ
    const lateRate = StatisticsUtils.calculateRate(totalLate, totalScheduled);
    const onTimeRate = StatisticsUtils.calculateRate(totalOnTime, totalScheduled);
    const absentRate = StatisticsUtils.calculateRate(totalAbsent, totalScheduled);
    const excusedRate = StatisticsUtils.calculateRate(totalExcused, totalScheduled);
    const businessTripRate = StatisticsUtils.calculateRate(totalBusinessTrip, totalScheduled);
    const nonAttendanceRate = StatisticsUtils.calculateRate(totalNonAttendance, totalScheduled);
    const checkedInRate = StatisticsUtils.calculateRate(totalCheckedIn, totalScheduled);

    return {
      totalScheduled,
      totalCheckedIn,
      totalOnTime,
      totalLate,
      totalExcused,
      totalBusinessTrip,
      totalAbsent,
      totalNonAttendance,
      totalOnDuty,
      lateRate,
      onTimeRate,
      absentRate,
      excusedRate,
      businessTripRate,
      nonAttendanceRate,
      checkedInRate
    };
  }

  /**
   * Tính toán số lượng cán bộ đang trực ban
   * @param {Array} dutyShifts - Danh sách duty shifts
   * @returns {Number} Số lượng cán bộ đang trực ban
   */
  calculateTotalOfficersOnDuty(dutyShifts) {
    const currentTime = Date.now();
    // Nhóm dutyShifts theo officer
    const shiftsByOfficer = {};
    dutyShifts.forEach(dutyShift => {
      const officerId = dutyShift.officer._id.toString();
      if (!shiftsByOfficer[officerId]) {
        shiftsByOfficer[officerId] = [];
      }
      shiftsByOfficer[officerId].push(dutyShift);
    });

    // Đếm số cán bộ đang trực ban theo shiftsByOfficer và thời gian hiện tại
    const totalOnDuty = Object.keys(shiftsByOfficer).filter(officerId => {
      const shifts = shiftsByOfficer[officerId];
      return shifts.some(shift => {
        return shift.startTime <= currentTime && shift.endTime >= currentTime;
      });
    }).length;

    return totalOnDuty;
  }

  /**
   * Tính toán thống kê cán bộ điểm danh theo ca
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @param {Array} workSchedules - Danh sách work schedules
   * @returns {Object} Thống kê theo ca
   */
  calculateAttendanceByShift(attendanceRecords, workSchedules) {
    const result = {};
    const attendanceService = require('./attendanceService');

    ['morning', 'afternoon'].forEach(shift => {
      const scheduled = workSchedules.filter(s => s.shifts.some(item => item.type === shift)).length;
      const checkedIn = attendanceRecords.filter(r => r.shift === shift).length;
      const late = attendanceRecords.filter(r => r.shift === shift && r.status === 'late').length;
      const onTime = attendanceRecords.filter(r => r.shift === shift && r.status === 'on_time').length;

      let absent = 0;
      let excused = 0;
      let businessTrip = 0;
      let nonAttendance = 0;

      workSchedules.forEach(schedule => {
        schedule.shifts.forEach(shiftItem => {
          if (shiftItem.type === shift) {
            // Kiểm tra xem có attendance record cho shift này không
            const hasAttendance = attendanceRecords.some(record =>
              record.user._id.toString() === schedule.user._id.toString() &&
              record.date === schedule.date &&
              record.shift === shift
            );

            if (!hasAttendance) {
              // Sử dụng logic từ attendanceService để xác định trạng thái chính xác
              const shiftStatus = attendanceService.getShiftStatus(shiftItem, null, schedule.date);

              if (shiftStatus === 'absent') {
                absent++;
              } else if (shiftStatus === 'excused') {
                excused++;
              } else if (shiftStatus === 'business_trip') {
                businessTrip++;
              } else if (shiftStatus === 'nonattendance') {
                nonAttendance++;
              } else {
                // Fallback cho các trạng thái khác
                if (shiftItem.status === 'absent') {
                  absent++;
                } else if (shiftItem.status === 'excused') {
                  excused++;
                } else if (shiftItem.status === 'business_trip') {
                  businessTrip++;
                } else {
                  nonAttendance++;
                }
              }
            }
          }
        });
      });
      const checkedInRate = StatisticsUtils.calculateRate(checkedIn, scheduled);
      const lateRate = StatisticsUtils.calculateRate(late, scheduled);
      const onTimeRate = StatisticsUtils.calculateRate(onTime, scheduled);
      const absentRate = StatisticsUtils.calculateRate(absent, scheduled);
      const excusedRate = StatisticsUtils.calculateRate(excused, scheduled);
      const businessTripRate = StatisticsUtils.calculateRate(businessTrip, scheduled);
      const nonAttendanceRate = StatisticsUtils.calculateRate(nonAttendance, scheduled);

      result[shift] = {
        scheduled,
        checkedIn,
        late,
        onTime,
        absent,
        excused,
        businessTrip,
        nonAttendance,
        checkedInRate,
        lateRate,
        onTimeRate,
        absentRate,
        excusedRate,
        businessTripRate,
        nonAttendanceRate
      };
    });

    return result;
  }

  /**
   * Xác định ca làm việc hiện tại dựa trên thời gian
   * @returns {String} 'morning' hoặc 'afternoon'
   */
  getCurrentShift() {
    const now = new Date();
    const currentHour = now.getHours();

    // Ca sáng: 8:00 - 12:00, Ca chiều: 14:00 - 18:00
    // Nếu trước 13:00 thì là ca sáng, sau 13:00 là ca chiều
    return currentHour < 13 ? 'morning' : 'afternoon';
  }

  /**
   * Tính toán trạng thái làm việc của cán bộ (đã cải thiện)
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @param {Array} leaveRequests - Đơn xin nghỉ
   * @returns {Object} Thống kê trạng thái làm việc
   */
  calculateOfficerSummary(allOfficers, workSchedules, attendanceRecords, dutyShifts, leaveRequests) {
    const currentTime = Date.now();
    const DateUtils = require('../utils/dateUtils');
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();
    const currentShift = this.getCurrentShift();

    // Tạo map để tra cứu nhanh
    const scheduleMap = _.groupBy(workSchedules, 'user._id');

    // Lọc duty shifts đang diễn ra và đã được xác nhận (status = 1)
    const activeDutyMap = _.groupBy(dutyShifts.filter(s =>
      s.startTime <= currentTime &&
      s.endTime >= currentTime &&
      s.status === 1 // Chỉ tính ca trực đã được xác nhận
    ), 'officer._id');

    // Lọc leave requests đã được phê duyệt và đang trong thời gian hiệu lực
    const activeLeaveMap = _.groupBy(leaveRequests.filter(leave => {
      if (leave.status !== 'approved') return false;

      // Kiểm tra thời gian hiệu lực của đơn nghỉ
      const startDate = DateUtils.convertDDMMYYYYtoYYYYMMDD(leave.startDate);
      const endDate = leave.endDate ? DateUtils.convertDDMMYYYYtoYYYYMMDD(leave.endDate) : startDate;
      const currentDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(currentDate);

      return currentDateYYYYMMDD >= startDate && currentDateYYYYMMDD <= endDate;
    }), 'user._id');

    let working = 0, onDuty = 0, onLeave = 0, businessTrip = 0;
    const totalOfficers = allOfficers.length;

    allOfficers.forEach(officer => {
      const officerId = officer._id.toString();

      // Ưu tiên 1: Kiểm tra trực ban (cao nhất)
      if (activeDutyMap[officerId]) {
        onDuty++;
        return;
      }

      // Ưu tiên 2: Kiểm tra nghỉ phép từ leave requests
      if (activeLeaveMap[officerId]) {
        onLeave++;
        return;
      }

      // Ưu tiên 3: Kiểm tra lịch làm việc hôm nay
      const todaySchedules = scheduleMap[officerId]?.filter(s => s.date === currentDate) || [];

      if (todaySchedules.length > 0) {
        // Kiểm tra ca hiện tại trong lịch làm việc
        const currentShiftSchedule = todaySchedules[0].shifts?.find(shift =>
          shift.type === currentShift
        );

        if (currentShiftSchedule) {
          // Kiểm tra trạng thái ca làm việc
          if (currentShiftSchedule.status === 'excused') {
            onLeave++;
            return;
          }

          if (currentShiftSchedule.status === 'business_trip') {
            businessTrip++;
            return;
          }

          // Nếu có lịch làm việc và trạng thái không phải nghỉ/công tác thì tính là working
          // Bất kể đã điểm danh hay chưa
          working++;
          return;
        }
      }

      // Mặc định: không có thông tin cụ thể, không tính vào các nhóm chính
      // (có thể là không có lịch làm việc hoặc chưa điểm danh)
    });

    return {
      totalOfficers,
      working,
      onDuty,
      onLeave,
      businessTrip
    };
  }

  /**
   * Tính toán thống kê cán bộ theo khu vực
   * @param {Array} areas - Danh sách khu vực
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @returns {Array} Thống kê theo khu vực
   */
  async calculateOfficersByArea(areas, allOfficers, workSchedules, attendanceRecords, dutyShifts) {
    const currentTime = Date.now();

    const result = [];

    for (const area of areas) {
      // Lấy cán bộ thuộc khu vực này
      const areaOfficers = allOfficers.filter(officer =>
        officer.areas && officer.areas.some(a => a._id.toString() === area._id.toString())
      );

      const totalOfficers = areaOfficers.length;
      let workingOfficers = 0, onDutyOfficers = 0;

      // Tính toán trạng thái cho từng cán bộ
      areaOfficers.forEach(officer => {
        const officerId = officer._id.toString();

        // Kiểm tra trực ban
        const isOnDuty = dutyShifts.some(shift =>
          shift.officer._id.toString() === officerId &&
          shift.startTime <= currentTime &&
          shift.endTime >= currentTime
        );

        if (isOnDuty) {
          onDutyOfficers++;
        } else {
          // Kiểm tra điểm danh
          const hasAttendance = attendanceRecords.some(record =>
            record.user._id.toString() === officerId
          );

          if (hasAttendance) {
            workingOfficers++;
          }
        }
      });

      // Tính tỷ lệ điểm danh
      const attendanceRate = StatisticsUtils.calculateRate(workingOfficers, totalOfficers);

      result.push({
        areaId: area._id,
        areaName: area.name,
        level: area.level,
        summary: {
          totalOfficers,
          workingOfficers,
          onDutyOfficers,
          attendanceRate
        }
      });
    }

    return result;
  }

  /**
   * Tính toán thống kê tổng quan cho khu vực
   * @param {Array} areaStats - Thống kê theo khu vực
   * @returns {Object} Thống kê tổng quan
   */
  calculateAreaSummary(areaStats) {
    const totalAreas = areaStats.length;
    const totalOfficers = _.sumBy(areaStats, 'summary.totalOfficers');
    const averageOfficersPerArea = totalAreas > 0 ? Math.round(totalOfficers / totalAreas) : 0;

    // Tìm khu vực có mật độ cán bộ cao nhất
    const highestDensityArea = _.maxBy(areaStats, 'summary.totalOfficers');

    return {
      totalAreas,
      totalOfficers,
      averageOfficersPerArea,
      highestDensityArea: highestDensityArea ? {
        areaId: highestDensityArea.areaId,
        areaName: highestDensityArea.areaName,
        officerCount: highestDensityArea.summary.totalOfficers
      } : null
    };
  }

  /**
   * Tính toán thống kê báo cáo theo khu vực (UPDATED - sử dụng ReportDetail-first approach)
   * @param {Array} areas - Danh sách khu vực (chỉ level 1)
   * @param {Array} reports - Danh sách báo cáo (từ getReports đã được optimize)
   * @param {Array} allOfficers - Danh sách cán bộ
   * @returns {Array} Thống kê báo cáo theo khu vực
   */
  async calculateReportsByArea(areas, reports, allOfficers) {
    const reportIds = reports.map(r => r._id);

    // Sử dụng aggregation pipeline để tính toán thống kê hiệu quả với filter level = 1
    const pipeline = [
      // Match ReportDetails của các reports trong khoảng thời gian
      {
        $match: {
          reportId: { $in: reportIds }
        }
      },
      // Unwind areas để group theo từng area
      {
        $unwind: '$areas'
      },
      // Lookup Area để filter level = 1
      {
        $lookup: {
          from: 'areas',
          localField: 'areas',
          foreignField: '_id',
          as: 'areaInfo'
        }
      },
      {
        $unwind: '$areaInfo'
      },
      // Filter chỉ lấy areas có level = 1
      {
        $match: {
          'areaInfo.level': 1
        }
      },
      // Group theo area để tính thống kê
      {
        $group: {
          _id: '$areas',
          areaName: { $first: '$areaInfo.name' },
          areaLevel: { $first: '$areaInfo.level' },
          totalDetails: { $sum: 1 },
          uniqueReports: { $addToSet: '$reportId' }
        }
      },
      // Lookup để lấy thông tin Report
      {
        $lookup: {
          from: 'reports',
          localField: 'uniqueReports',
          foreignField: '_id',
          as: 'reports'
        }
      },
      // Project kết quả cuối cùng
      {
        $project: {
          areaId: '$_id',
          areaName: 1,
          areaLevel: 1,
          totalDetails: 1,
          totalReports: { $size: '$uniqueReports' },
          reports: 1
        }
      }
    ];

    const areaStatsFromAggregation = await ReportDetail.aggregate(pipeline);

    // Tạo map để lookup nhanh
    const areaStatsMap = areaStatsFromAggregation.reduce((map, stat) => {
      map[stat.areaId.toString()] = stat;
      return map;
    }, {});

    // Tạo map để lookup nhanh areas level 1
    const level1AreasMap = areas.filter(area => area.level === 1).reduce((map, area) => {
      map[area._id.toString()] = area;
      return map;
    }, {});

    const result = [];

    // Xử lý kết quả từ aggregation (đã được filter level = 1)
    for (const areaStat of areaStatsFromAggregation) {
      const areaId = areaStat.areaId.toString();
      const area = level1AreasMap[areaId];

      if (!area) continue; // Skip nếu không tìm thấy area info

      // Lấy cán bộ thuộc khu vực này
      const areaOfficers = allOfficers.filter(officer =>
        officer.areas && officer.areas.some(a => a._id.toString() === areaId)
      );

      // Lấy reports tương ứng với area này
      const areaReports = areaStat.reports || [];

      // Tính tổng số vụ việc = số lượng ReportDetail (1 ReportDetail = 1 vụ việc)
      const totalIncidents = areaStat.totalDetails;

      // Tính thống kê chi tiết
      const byReportType = _.countBy(areaReports, 'reportType');
      const byStatus = _.countBy(areaReports, 'status');
      const byWorkStatus = _.countBy(areaReports, 'workStatus');

      result.push({
        areaId: area._id,
        areaName: area.name,
        level: area.level,
        summary: {
          totalIncidents: totalIncidents, // Tổng số vụ việc = số ReportDetail
          totalReports: areaStat.totalReports, // Tổng số báo cáo
          totalOfficers: areaOfficers.length,
          incidentsPerOfficer: StatisticsUtils.calculateRate(totalIncidents, areaOfficers.length),
          reportsPerOfficer: StatisticsUtils.calculateRate(areaStat.totalReports, areaOfficers.length)
        },
        byReportType: {
          quick: byReportType.quick || 0,
          detail: byReportType.detail || 0
        },
        byStatus: {
          submitted: byStatus.submitted || 0,
          approved: byStatus.approved || 0,
          rejected: byStatus.rejected || 0
        },
        byWorkStatus: {
          pending: byWorkStatus.pending || 0,
          in_progress: byWorkStatus.in_progress || 0,
          completed: byWorkStatus.completed || 0,
          cancelled: byWorkStatus.cancelled || 0,
          on_hold: byWorkStatus.on_hold || 0
        }
      });
    }

    // Thêm các areas level 1 không có dữ liệu vào kết quả
    for (const area of areas.filter(a => a.level === 1)) {
      const areaId = area._id.toString();
      const existsInResult = result.some(r => r.areaId.toString() === areaId);

      if (!existsInResult) {
        // Lấy cán bộ thuộc khu vực này
        const areaOfficers = allOfficers.filter(officer =>
          officer.areas && officer.areas.some(a => a._id.toString() === areaId)
        );

        result.push({
          areaId: area._id,
          areaName: area.name,
          level: area.level,
          summary: {
            totalIncidents: 0, // Không có ReportDetail nào
            totalReports: 0,
            totalOfficers: areaOfficers.length,
            incidentsPerOfficer: 0,
            reportsPerOfficer: 0
          },
          byReportType: {
            quick: 0,
            detail: 0
          },
          byStatus: {
            submitted: 0,
            approved: 0,
            rejected: 0
          },
          byWorkStatus: {
            pending: 0,
            in_progress: 0,
            completed: 0,
            cancelled: 0,
            on_hold: 0
          }
        });
      }
    }

    return result;
  }

  /**
   * Tính toán thống kê tổng quan cho báo cáo theo khu vực
   * @param {Array} areaStats - Thống kê báo cáo theo khu vực
   * @returns {Object} Thống kê tổng quan
   */
  calculateReportAreaSummary(areaStats) {
    const totalAreas = areaStats.length;
    const totalIncidents = areaStats.reduce((sum, area) => sum + area.summary.totalIncidents, 0);
    const totalReports = areaStats.reduce((sum, area) => sum + area.summary.totalReports, 0);
    const totalOfficers = areaStats.reduce((sum, area) => sum + area.summary.totalOfficers, 0);

    const averageIncidentsPerArea = StatisticsUtils.calculateRate(totalIncidents, totalAreas);
    const averageIncidentsPerOfficer = StatisticsUtils.calculateRate(totalIncidents, totalOfficers);
    const averageReportsPerArea = StatisticsUtils.calculateRate(totalReports, totalAreas);
    const averageReportsPerOfficer = StatisticsUtils.calculateRate(totalReports, totalOfficers);

    // Tìm khu vực có nhiều vụ việc nhất
    const maxIncidentsArea = areaStats.reduce((max, area) =>
      area.summary.totalIncidents > max.totalIncidents ?
        { areaName: area.areaName, totalIncidents: area.summary.totalIncidents } : max,
      { areaName: '', totalIncidents: 0 }
    );

    return {
      totalAreas,
      totalIncidents, // Tổng số vụ việc (từ metrics)
      totalReports,   // Tổng số báo cáo
      totalOfficers,
      averageIncidentsPerArea,
      averageIncidentsPerOfficer,
      averageReportsPerArea,
      averageReportsPerOfficer,
      maxIncidentsArea
    };
  }

  /**
   * Lấy reports với details sử dụng efficient $lookup operations
   * @param {Object} period - Khoảng thời gian
   * @param {Array} reportIds - Danh sách report IDs (optional)
   * @param {String} areaId - ID khu vực để filter (optional)
   * @returns {Array} Danh sách reports với details populated hiệu quả
   */
  async getReportsWithDetailsOptimized(period, reportIds = null, areaId = null) {
    const matchStage = {
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' }
    };

    // Thêm filter theo reportIds nếu có
    if (reportIds && reportIds.length > 0) {
      matchStage._id = { $in: reportIds };
    }

    const pipeline = [
      // Match reports trong khoảng thời gian
      { $match: matchStage },

      // Lookup ReportDetails với efficient indexing
      {
        $lookup: {
          from: 'reportdetails',
          let: { reportId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$reportId', '$$reportId'] },
                ...(areaId ? { areas: require('mongoose').Types.ObjectId(areaId) } : {})
              }
            },
            // Lookup areas trong ReportDetail
            {
              $lookup: {
                from: 'areas',
                localField: 'areas',
                foreignField: '_id',
                as: 'areaDetails'
              }
            },
            // Project ReportDetail fields
            {
              $project: {
                location: {
                  areas: '$areaDetails',
                  coordinates: '$coordinates',
                  address: '$address'
                },
                time: 1,
                description: 1
              }
            }
          ],
          as: 'details'
        }
      },

      // Lookup JobType
      {
        $lookup: {
          from: 'jobtypes',
          localField: 'jobType',
          foreignField: '_id',
          as: 'jobTypeDetails'
        }
      },

      // Lookup CreatedBy User
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'createdByDetails'
        }
      },

      // Project final result
      {
        $project: {
          title: 1,
          reportType: 1,
          status: 1,
          workStatus: 1,
          metrics: 1,
          summary: 1,
          createdAt: 1,
          updatedAt: 1,
          jobType: { $arrayElemAt: ['$jobTypeDetails', 0] },
          createdBy: { $arrayElemAt: ['$createdByDetails', 0] },
          details: 1,
          detailsCount: { $size: '$details' }
        }
      }
    ];

    return await Report.aggregate(pipeline);
  }

  /**
   * Tính toán thống kê tổng quan báo cáo (UPDATED - sử dụng ReportDetail count)
   * @param {Array} reports - Danh sách báo cáo với details populated từ ReportDetail
   * @returns {Object} Thống kê tổng quan
   */
  calculateReportsSummary(reports) {
    const totalReports = reports.length;

    // Tính tổng số vụ việc = tổng số ReportDetail (1 ReportDetail = 1 vụ việc)
    const totalIncidents = reports.reduce((total, report) => {
      return total + (report.details ? report.details.length : 0);
    }, 0);

    const quickReports = reports.filter(r => r.reportType === 'quick').length;
    const detailReports = reports.filter(r => r.reportType === 'detail').length;

    // Tính số vụ việc theo loại báo cáo (đếm ReportDetail)
    const quickIncidents = reports
      .filter(r => r.reportType === 'quick')
      .reduce((total, report) => total + (report.details ? report.details.length : 0), 0);

    const detailIncidents = reports
      .filter(r => r.reportType === 'detail')
      .reduce((total, report) => total + (report.details ? report.details.length : 0), 0);

    const submittedReports = reports.filter(r => r.status === 'submitted').length;
    const approvedReports = reports.filter(r => r.status === 'approved').length;
    const rejectedReports = reports.filter(r => r.status === 'rejected').length;

    const completedReports = reports.filter(r => r.workStatus === 'completed').length;
    const inProgressReports = reports.filter(r => r.workStatus === 'in_progress').length;
    const pendingReports = reports.filter(r => r.workStatus === 'pending').length;

    return {
      totalReports,
      totalIncidents, // Tổng số vụ việc = tổng ReportDetail
      quickReports,
      detailReports,
      quickIncidents, // Số vụ việc từ báo cáo nhanh = ReportDetail của quick reports
      detailIncidents, // Số vụ việc từ báo cáo chi tiết = ReportDetail của detail reports
      submittedReports,
      approvedReports,
      rejectedReports,
      completedReports,
      inProgressReports,
      pendingReports,
      quickReportRate: StatisticsUtils.calculateRate(quickReports, totalReports),
      quickIncidentRate: StatisticsUtils.calculateRate(quickIncidents, totalIncidents),
      approvalRate: StatisticsUtils.calculateRate(approvedReports, totalReports),
      completionRate: StatisticsUtils.calculateRate(completedReports, totalReports),
      incidentsPerReport: StatisticsUtils.calculateRate(totalIncidents, totalReports)
    };
  }

  /**
   * Tính toán thống kê báo cáo theo trạng thái
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê theo trạng thái
   */
  calculateReportsByStatus(reports) {
    const byStatus = _.countBy(reports, 'status');
    const byWorkStatus = _.countBy(reports, 'workStatus');

    return {
      status: {
        draft: byStatus.draft || 0,
        submitted: byStatus.submitted || 0,
        approved: byStatus.approved || 0,
        rejected: byStatus.rejected || 0
      },
      workStatus: {
        pending: byWorkStatus.pending || 0,
        in_progress: byWorkStatus.in_progress || 0,
        completed: byWorkStatus.completed || 0,
        cancelled: byWorkStatus.cancelled || 0,
        on_hold: byWorkStatus.on_hold || 0
      }
    };
  }

  /**
   * Tính toán thống kê báo cáo theo loại
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê theo loại báo cáo
   */
  calculateReportsByType(reports) {
    const byType = _.countBy(reports, 'reportType');
    const total = reports.length;

    return {
      quick: {
        count: byType.quick || 0,
        percentage: StatisticsUtils.calculateRate(byType.quick || 0, total)
      },
      detail: {
        count: byType.detail || 0,
        percentage: StatisticsUtils.calculateRate(byType.detail || 0, total)
      }
    };
  }

  /**
   * Tính toán thống kê báo cáo theo loại công việc
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Array} Thống kê theo JobType
   */
  calculateReportsByJobType(reports) {
    const byJobType = {};
    const total = reports.length;

    reports.forEach(report => {
      if (report.jobType) {
        const jobTypeName = report.jobType.name || 'Không xác định';
        const jobTypeId = report.jobType._id || 'unknown';

        if (!byJobType[jobTypeId]) {
          byJobType[jobTypeId] = {
            jobTypeId,
            jobTypeName,
            count: 0
          };
        }
        byJobType[jobTypeId].count++;
      }
    });

    return Object.values(byJobType).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculateRate(item.count, total),
      color: this.getJobTypeColor(item.jobTypeName)
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Tính tổng số vụ việc từ metrics của các báo cáo
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Number} Tổng số vụ việc
   */
  calculateTotalIncidentsFromReports(reports) {
    let totalIncidents = 0;

    reports.forEach(report => {
      if (report.metrics && typeof report.metrics === 'object') {
        // Tính tổng tất cả các giá trị số trong metrics
        Object.keys(report.metrics).forEach(key => {
          if (report.jobType && report.jobType.quickReportTemplate && report.jobType.quickReportTemplate.metrics && report.jobType.quickReportTemplate.metrics[key] && report.jobType.quickReportTemplate.metrics[key].needsDetails) {
            const value = report.metrics[key]
            if (typeof value === 'number' && value > 0) {
              totalIncidents += value;
            }
          } else {
            const value = report.metrics[key]
            if (typeof value === 'number' && value > 0) {
              totalIncidents += value;
            }
          }
        })
      }
    });

    return totalIncidents;
  }

  /**
   * Mapping màu sắc cho JobType dựa trên tên
   * @param {String} jobTypeName - Tên JobType
   * @returns {String} Mã màu hex
   */
  getJobTypeColor(jobTypeName) {
    if (!jobTypeName) return '#6C757D'; // Màu mặc định

    const name = jobTypeName.toLowerCase();

    // ANTT hoặc an ninh trật tự
    if (name.includes('antt') || name.includes('an ninh trật tự')) {
      return '#007CFE';
    }

    // Tai nạn giao thông hoặc giao thông
    if (name.includes('tai nạn giao thông') || name.includes('giao thông')) {
      return '#FFC107';
    }

    // Cháy nổ hoặc cháy
    if (name.includes('cháy nổ') || name.includes('cháy')) {
      return '#D30500';
    }

    // Màu mặc định
    return '#6C757D';
  }

  /**
   * Lấy tất cả JobType có chartTypes chứa 'highlight' để tạo default stats (có color)
   * @returns {Array} Danh sách JobType mặc định với giá trị 0 và color
   */
  async getDefaultHighlightJobTypeStats() {
    const JobType = require('../models/jobType');

    const highlightJobTypes = await JobType.find({
      'quickReportTemplate.chartTypes': 'highlight',
      status: 1,
      deletedAt: { $exists: false }
    })
      .select('_id name')
      .lean();

    return highlightJobTypes.map(jobType => ({
      jobTypeId: jobType._id.toString(),
      jobTypeName: jobType.name || 'Không xác định',
      totalReports: 0,
      totalIncidents: 0,
      percentage: 0,
      color: this.getJobTypeColor(jobType.name)
    }));
  }

  /**
   * Lấy tất cả JobType để tạo default stats (không có color)
   * @returns {Array} Danh sách JobType mặc định với giá trị 0
   */
  async getDefaultJobTypeStats() {
    const JobType = require('../models/jobType');

    const jobTypes = await JobType.find({
      status: 1,
      deletedAt: { $exists: false }
    })
      .select('_id name')
      .lean();

    return jobTypes.map(jobType => ({
      jobTypeId: jobType._id.toString(),
      jobTypeName: jobType.name || 'Không xác định',
      totalReports: 0,
      totalIncidents: 0,
      percentage: 0
    }));
  }

  /**
   * Tính số vụ việc theo JobType cho Highlight (có trường color)
   * @param {Array} reports - Danh sách báo cáo với details populated từ ReportDetail
   * @returns {Array} Thống kê số vụ việc theo JobType với color
   */
  async calculateIncidentsByJobTypeWithColor(reports) {
    // Lấy tất cả JobType mặc định
    const defaultStats = await this.getDefaultHighlightJobTypeStats();
    const byJobType = {};

    // Khởi tạo với giá trị mặc định
    defaultStats.forEach(stat => {
      byJobType[stat.jobTypeId] = { ...stat };
    });

    const totalIncidents = reports.reduce((total, report) => total + (report.details ? report.details.length : 0), 0);

    // Cập nhật với dữ liệu thực tế
    reports.forEach(report => {
      if (report.jobType) {
        const jobTypeId = report.jobType._id.toString();
        const jobTypeName = report.jobType.name || 'Không xác định';
        const incidents = report.details ? report.details.length : 0;

        if (!byJobType[jobTypeId]) {
          byJobType[jobTypeId] = {
            jobTypeId,
            jobTypeName,
            totalReports: 0,
            totalIncidents: 0
          };
        }

        byJobType[jobTypeId].totalReports++;
        byJobType[jobTypeId].totalIncidents += incidents;
      }
    });

    // Chuyển đổi thành array và tính phần trăm với color
    return Object.values(byJobType).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidents),
      color: this.getJobTypeColor(item.jobTypeName)
    })).sort((a, b) => b.totalIncidents - a.totalIncidents);
  }

  /**
   * Tính số vụ việc theo JobType (UPDATED - luôn trả về tất cả JobType với giá trị mặc định, không có color)
   * @param {Array} reports - Danh sách báo cáo với details populated từ ReportDetail
   * @returns {Array} Thống kê số vụ việc theo JobType
   */
  async calculateIncidentsByJobType(reports) {
    // Lấy tất cả JobType mặc định (không có color)
    const defaultStats = await this.getDefaultJobTypeStats();
    const byJobType = {};

    // Khởi tạo với giá trị mặc định
    defaultStats.forEach(stat => {
      byJobType[stat.jobTypeId] = { ...stat };
    });

    const totalIncidents = reports.reduce((total, report) => total + (report.details ? report.details.length : 0), 0);

    // Cập nhật với dữ liệu thực tế
    reports.forEach(report => {
      if (report.jobType) {
        const jobTypeId = report.jobType._id.toString();
        const jobTypeName = report.jobType.name || 'Không xác định';
        const incidents = report.details ? report.details.length : 0;

        if (!byJobType[jobTypeId]) {
          byJobType[jobTypeId] = {
            jobTypeId,
            jobTypeName,
            totalReports: 0,
            totalIncidents: 0
          };
        }

        byJobType[jobTypeId].totalReports++;
        byJobType[jobTypeId].totalIncidents += incidents;
      }
    });

    // Chuyển đổi thành array và tính phần trăm (không có color)
    return Object.values(byJobType).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidents)
    })).sort((a, b) => b.totalIncidents - a.totalIncidents);
  }

  /**
   * Tính thống kê JobType cho một khu vực cụ thể (UPDATED - sử dụng ReportDetail count)
   * @param {Array} areaReports - Danh sách báo cáo của khu vực với details populated
   * @returns {Array} Thống kê JobType cho khu vực
   */
  calculateJobTypeStatsForArea(areaReports) {
    if (!areaReports || areaReports.length === 0) {
      return [];
    }

    const byJobType = {};
    const totalIncidents = areaReports.reduce((total, report) => total + (report.details ? report.details.length : 0), 0);

    areaReports.forEach(report => {
      if (report.jobType) {
        const jobTypeId = report.jobType._id.toString();
        const jobTypeName = report.jobType.name || 'Không xác định';
        const incidents = report.details ? report.details.length : 0;

        if (!byJobType[jobTypeId]) {
          byJobType[jobTypeId] = {
            jobTypeId,
            jobTypeName,
            totalReports: 0,
            totalIncidents: 0
          };
        }

        byJobType[jobTypeId].totalReports++;
        byJobType[jobTypeId].totalIncidents += incidents;
      }
    });

    // Chuyển đổi thành array và tính phần trăm dựa trên tổng incidents của area
    return Object.values(byJobType).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidents),
      color: this.getJobTypeColor(item.jobTypeName)
    })).sort((a, b) => b.totalIncidents - a.totalIncidents);
  }

  /**
   * Tính thống kê JobType cho một khu vực từ ReportDetail cụ thể (UPDATED - luôn trả về tất cả JobType)
   * @param {Array} areaReportDetails - Danh sách {detail, report} của area cụ thể
   * @param {Number} totalIncidentsInArea - Tổng incidents trong area để tính phần trăm
   * @returns {Promise<Array>} Thống kê JobType cho khu vực
   */
  async calculateJobTypeStatsForAreaFromDetails(areaReportDetails, totalIncidentsInArea) {
    // Lấy tất cả JobType mặc định
    const defaultStats = await this.getDefaultHighlightJobTypeStats();
    const byJobType = {};

    // Khởi tạo với giá trị mặc định
    defaultStats.forEach(stat => {
      byJobType[stat.jobTypeId] = {
        jobTypeId: stat.jobTypeId,
        jobTypeName: stat.jobTypeName,
        totalReports: new Set(), // Sử dụng Set để tránh đếm trùng report
        totalIncidents: 0
      };
    });

    // Cập nhật với dữ liệu thực tế nếu có
    if (areaReportDetails && areaReportDetails.length > 0) {
      areaReportDetails.forEach(({ detail, report }) => {
        if (report && report.jobType) {
          const jobTypeId = report.jobType._id.toString();
          const jobTypeName = report.jobType.name || 'Không xác định';
          const reportId = report._id.toString();

          if (!byJobType[jobTypeId]) {
            byJobType[jobTypeId] = {
              jobTypeId,
              jobTypeName,
              totalReports: new Set(), // Sử dụng Set để tránh đếm trùng report
              totalIncidents: 0
            };
          }

          // Thêm report vào Set (tự động tránh trùng lặp)
          byJobType[jobTypeId].totalReports.add(reportId);

          // Mỗi ReportDetail = 1 incident
          byJobType[jobTypeId].totalIncidents += 1;
        }
      });
    }

    // Chuyển đổi thành array và tính phần trăm dựa trên tổng incidents của area
    return Object.values(byJobType).map(item => ({
      jobTypeId: item.jobTypeId,
      jobTypeName: item.jobTypeName,
      totalReports: item.totalReports.size, // Chuyển Set thành số lượng
      totalIncidents: item.totalIncidents,
      percentage: StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidentsInArea),
      color: this.getJobTypeColor(item.jobTypeName)
    })).sort((a, b) => b.totalIncidents - a.totalIncidents);
  }

  /**
   * Tính toán thống kê vụ việc theo khu vực (Area) với chi tiết theo JobType (UPDATED - sử dụng ReportDetail count)
   * @param {Array} reports - Danh sách báo cáo với details populated từ ReportDetail
   * @returns {Promise<Array>} Thống kê theo Area bao gồm chi tiết JobType
   */
  async calculateIncidentsByArea(reports) {
    const byArea = {};
    const areaReportDetailsMap = {}; // Lưu ReportDetail theo area để tính JobType stats chính xác

    // Lấy tất cả ReportDetails cho các reports này
    const reportIds = reports.map(r => r._id);
    const reportDetails = await ReportDetail.find({
      reportId: { $in: reportIds }
    }).populate('areas', 'name level').lean();

    // Tạo map reports để lookup nhanh
    const reportsMap = reports.reduce((map, report) => {
      map[report._id.toString()] = report;
      return map;
    }, {});

    // Duyệt qua từng ReportDetail để tính incidents theo area
    reportDetails.forEach(detail => {
      const reportId = detail.reportId.toString();
      const report = reportsMap[reportId];

      if (!report) return;

      // Lọc chỉ lấy areas level 1
      const level1Areas = detail.areas ? detail.areas.filter(area => area.level === 1) : [];

      level1Areas.forEach(area => {
        const areaId = area._id.toString();
        const areaName = area.name || 'Không xác định';

        // Khởi tạo area nếu chưa có
        if (!byArea[areaId]) {
          byArea[areaId] = {
            areaId,
            areaName,
            totalReports: new Set(), // Sử dụng Set để tránh đếm trùng report
            totalIncidents: 0
          };
        }

        // Thêm report vào Set (tự động tránh trùng lặp)
        byArea[areaId].totalReports.add(reportId);

        // Mỗi ReportDetail = 1 incident
        byArea[areaId].totalIncidents += 1;

        // Lưu ReportDetail cụ thể cho area này để tính JobType stats chính xác
        if (!areaReportDetailsMap[areaId]) {
          areaReportDetailsMap[areaId] = [];
        }
        areaReportDetailsMap[areaId].push({
          detail: detail,
          report: report
        });
      });

      // Nếu ReportDetail không có area level 1 nào, gán vào "Không xác định"
      if (level1Areas.length === 0) {
        const areaId = 'unassigned';
        const areaName = 'Không xác định';

        if (!byArea[areaId]) {
          byArea[areaId] = {
            areaId,
            areaName,
            totalReports: new Set(),
            totalIncidents: 0
          };
        }

        byArea[areaId].totalReports.add(reportId);
        byArea[areaId].totalIncidents += 1;

        if (!areaReportDetailsMap[areaId]) {
          areaReportDetailsMap[areaId] = [];
        }
        areaReportDetailsMap[areaId].push({
          detail: detail,
          report: report
        });
      }
    });

    // Tính tổng incidents để tính phần trăm
    const totalIncidents = Object.values(byArea).reduce((total, area) => total + area.totalIncidents, 0);

    // Chuyển đổi thành array và tính phần trăm, đồng thời thêm thống kê jobType cho mỗi area
    const areaItems = Object.values(byArea);

    // Tính toán byJobType cho tất cả areas song song
    const areaStatsPromises = areaItems.map(async (item) => {
      // Chuyển Set thành số lượng
      const totalReports = item.totalReports.size;

      // Lấy ReportDetail cụ thể của area này để tính JobType stats chính xác
      const areaReportDetails = areaReportDetailsMap[item.areaId] || [];
      const byJobType = await this.calculateJobTypeStatsForAreaFromDetails(areaReportDetails, item.totalIncidents);

      return {
        areaId: item.areaId,
        areaName: item.areaName,
        totalReports: totalReports,
        totalIncidents: item.totalIncidents,
        percentage: StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidents),
        byJobType // Thêm thống kê chi tiết theo jobType
      };
    });

    const areaStats = await Promise.all(areaStatsPromises);
    return areaStats.sort((a, b) => b.totalIncidents - a.totalIncidents);
  }

  /**
   * Tính toán thống kê vụ việc theo ngày với chi tiết theo JobType
   * Luôn lấy 7 ngày gần nhất tính từ hôm nay
   * @param {Array} reports - Danh sách báo cáo highlight
   * @returns {Array} Thống kê theo ngày bao gồm chi tiết JobType (7 ngày gần nhất từ hôm nay)
   */
  async calculateIncidentsByDay(area = null) {
    // Tạo danh sách 7 ngày gần nhất tính từ hôm nay
    const last7Days = [];
    const today = moment();

    for (let i = 0; i < 7; i++) {
      const day = moment(today).subtract(i, 'days');
      const dayKey = day.format('DD-MM-YYYY');
      const dayName = day.format('dddd, DD/MM/YYYY');

      last7Days.push({
        dayKey,
        dayName,
        totalReports: 0,
        totalIncidents: 0,
        byJobType: []
      });
    }

    last7Days.reverse();

    const period = StatisticsUtils.getTimeRange('custom', last7Days[0].dayKey, last7Days[last7Days.length - 1].dayKey);

      // Lấy dữ liệu báo cáo highlight trong khoảng thời gian hiện tại
    const currentReports = await this.getHighlightReports(period, area);

    // Tạo map để lưu trữ reports theo ngày
    const dayReportsMap = {};

    // Khởi tạo map cho 7 ngày
    last7Days.forEach(day => {
      dayReportsMap[day.dayKey] = [];
    });

    // Lọc và phân loại reports theo 7 ngày gần nhất
    currentReports.forEach(report => {
      const reportDate = new Date(report.createdAt);
      const dayKey = moment(reportDate).format('DD-MM-YYYY');

      // Chỉ xử lý reports trong 7 ngày gần nhất
      if (dayReportsMap.hasOwnProperty(dayKey)) {
        dayReportsMap[dayKey].push(report);

        // Tìm và cập nhật thông tin cho ngày tương ứng
        const dayIndex = last7Days.findIndex(d => d.dayKey === dayKey);
        if (dayIndex !== -1) {
          const reportIncidents = report.details ? report.details.length : 0;
          last7Days[dayIndex].totalReports++;
          last7Days[dayIndex].totalIncidents += reportIncidents;
        }
      }
    });

    // Tính tổng incidents trong 7 ngày để tính phần trăm
    const totalIncidents = last7Days.reduce((sum, day) => sum + day.totalIncidents, 0);

    // Tính toán thống kê jobType và phần trăm cho mỗi ngày
    return last7Days.map(item => {
      const dayReports = dayReportsMap[item.dayKey] || [];
      const byJobType = this.calculateJobTypeStatsForArea(dayReports);

      return {
        ...item,
        totalIncidents: Math.round(item.totalIncidents), // Đảm bảo là số nguyên
        percentage: totalIncidents > 0 ? StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidents) : 0,
        byJobType // Thêm thống kê chi tiết theo jobType
      };
    });
    // Không cần sort vì đã được sắp xếp theo thứ tự từ hôm nay về trước
  }

  /**
   * Tính toán tỷ lệ thay đổi so với khoảng thời gian trước
   * @param {Array} currentReports - Báo cáo khoảng thời gian hiện tại
   * @param {Array} previousReports - Báo cáo khoảng thời gian trước
   * @param {Array} currentByJobType - Thống kê hiện tại theo JobType
   * @param {Array} currentByArea - Thống kê hiện tại theo Area
   * @param {Boolean} withColor - Có sử dụng color hay không (default: false)
   * @returns {Object} Tỷ lệ thay đổi
   */
  async calculateChangeRate(currentReports, previousReports, currentByJobType, currentByArea, withColor = false) {
    const currentTotal = currentReports.reduce((total, report) => total + (report.details ? report.details.length : 0), 0);
    const previousTotal = previousReports.reduce((total, report) => total + (report.details ? report.details.length : 0), 0);

    // Tính tỷ lệ thay đổi tổng thể
    const totalChangeRate = previousTotal > 0 ?
      Math.round(((currentTotal - previousTotal) / previousTotal) * 100) :
      (currentTotal > 0 ? 100 : 0);

    // Tính tỷ lệ thay đổi theo JobType
    const previousByJobType = withColor ?
      await this.calculateIncidentsByJobTypeWithColor(previousReports) :
      await this.calculateIncidentsByJobType(previousReports);

    const jobTypeChanges = currentByJobType.map(current => {
      const previous = previousByJobType.find(p => p.jobTypeId === current.jobTypeId);
      const previousIncidents = previous ? previous.totalIncidents : 0;
      const changeRate = previousIncidents > 0 ?
        Math.round(((current.totalIncidents - previousIncidents) / previousIncidents) * 100) :
        (current.totalIncidents > 0 ? 100 : 0);

      return {
        ...current,
        previousIncidents,
        changeRate: Math.abs(changeRate),
        changeType: changeRate > 0 ? 'increase' : changeRate < 0 ? 'decrease' : 'stable'
      };
    });

    // Tính tỷ lệ thay đổi theo Area
    const previousByArea = await this.calculateIncidentsByArea(previousReports);
    const areaChanges = currentByArea.map(current => {
      const previous = previousByArea.find(p => p.areaId === current.areaId);
      const previousIncidents = previous ? previous.totalIncidents : 0;
      const changeRate = previousIncidents > 0 ?
        Math.round(((current.totalIncidents - previousIncidents) / previousIncidents) * 100) :
        (current.totalIncidents > 0 ? 100 : 0);

      return {
        ...current,
        previousIncidents,
        changeRate: Math.abs(changeRate),
        changeType: changeRate > 0 ? 'increase' : changeRate < 0 ? 'decrease' : 'stable'
      };
    });

    return {
      total: {
        current: currentTotal,
        previous: previousTotal,
        changeRate: totalChangeRate,
        changeType: totalChangeRate > 0 ? 'increase' : totalChangeRate < 0 ? 'decrease' : 'stable'
      },
      byJobType: jobTypeChanges,
      byArea: areaChanges
    };
  }

  /**
   * Tính toán thống kê trạng thái chi tiết
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê trạng thái chi tiết
   */
  calculateDetailedStatusStats(reports) {
    const total = reports.length;
    const byStatus = _.countBy(reports, 'status');
    const byWorkStatus = _.countBy(reports, 'workStatus');
    const byReportType = _.countBy(reports, 'reportType');

    // Thống kê theo status
    const statusBreakdown = {
      draft: {
        count: byStatus.draft || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.draft || 0, total)
      },
      submitted: {
        count: byStatus.submitted || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.submitted || 0, total)
      },
      approved: {
        count: byStatus.approved || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.approved || 0, total)
      },
      rejected: {
        count: byStatus.rejected || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.rejected || 0, total)
      }
    };

    // Thống kê theo workStatus
    const workStatusBreakdown = {
      pending: {
        count: byWorkStatus.pending || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.pending || 0, total)
      },
      in_progress: {
        count: byWorkStatus.in_progress || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.in_progress || 0, total)
      },
      completed: {
        count: byWorkStatus.completed || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.completed || 0, total)
      },
      cancelled: {
        count: byWorkStatus.cancelled || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.cancelled || 0, total)
      },
      on_hold: {
        count: byWorkStatus.on_hold || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.on_hold || 0, total)
      }
    };

    return {
      total,
      statusBreakdown,
      workStatusBreakdown,
      reportTypeBreakdown: {
        quick: {
          count: byReportType.quick || 0,
          percentage: StatisticsUtils.calculateRate(byReportType.quick || 0, total)
        },
        detail: {
          count: byReportType.detail || 0,
          percentage: StatisticsUtils.calculateRate(byReportType.detail || 0, total)
        }
      }
    };
  }

  /**
   * Tính toán thống kê workflow
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê workflow
   */
  calculateWorkflowStats(reports) {
    const total = reports.length;

    // Báo cáo đã hoàn thành workflow (approved hoặc rejected)
    const completedWorkflow = reports.filter(r =>
      r.status === 'approved' || r.status === 'rejected'
    ).length;

    // Báo cáo đang chờ xử lý
    const pendingWorkflow = reports.filter(r => r.status === 'submitted').length;

    // Báo cáo nháp
    const draftReports = reports.filter(r => r.status === 'draft').length;

    // Tỷ lệ approval
    const approvedReports = reports.filter(r => r.status === 'approved').length;
    const submittedReports = reports.filter(r => r.status !== 'draft').length;
    const approvalRate = StatisticsUtils.calculateRate(approvedReports, submittedReports);

    // Tỷ lệ completion (work status)
    const completedWork = reports.filter(r => r.workStatus === 'completed').length;
    const completionRate = StatisticsUtils.calculateRate(completedWork, total);

    return {
      total,
      completedWorkflow,
      pendingWorkflow,
      draftReports,
      approvalRate,
      completionRate,
      workflowEfficiency: StatisticsUtils.calculateRate(completedWorkflow, total)
    };
  }

  /**
   * Tính toán thống kê thời gian xử lý
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê thời gian xử lý
   */
  calculateProcessingTimeStats(reports) {
    const processedReports = reports.filter(r =>
      r.status === 'approved' || r.status === 'rejected'
    );

    if (processedReports.length === 0) {
      return {
        averageProcessingTime: 0,
        fastestProcessing: 0,
        slowestProcessing: 0,
        processedCount: 0
      };
    }

    // Tính thời gian xử lý (giả sử có updatedAt)
    const processingTimes = processedReports.map(report => {
      // Thời gian từ khi tạo đến khi được approve/reject
      return report.updatedAt - report.createdAt;
    }).filter(time => time > 0);

    if (processingTimes.length === 0) {
      return {
        averageProcessingTime: 0,
        fastestProcessing: 0,
        slowestProcessing: 0,
        processedCount: processedReports.length
      };
    }

    const averageProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
    const fastestProcessing = Math.min(...processingTimes);
    const slowestProcessing = Math.max(...processingTimes);

    // Convert từ milliseconds sang hours
    return {
      averageProcessingTime: Math.round(averageProcessingTime / (1000 * 60 * 60) * 100) / 100,
      fastestProcessing: Math.round(fastestProcessing / (1000 * 60 * 60) * 100) / 100,
      slowestProcessing: Math.round(slowestProcessing / (1000 * 60 * 60) * 100) / 100,
      processedCount: processedReports.length
    };
  }

  /**
   * Lấy tất cả văn bản trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách văn bản
   */
  async getDocuments(period) {
    return await Report.find({
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      jobType: CONSTANTS.DOCUMENT_JOB_TYPE_ID, // Chỉ lấy reports có tính chất văn bản
      deletedAt: { $exists: false },
      status: { $ne: 'draft' }
    })
      .populate('createdBy', 'name idNumber units')
      .populate('unit', 'name')
      .lean();
  }

  /**
   * Lấy báo cáo công văn mới nhất để lấy số công văn chưa trả lời
   * @returns {Object|null} Báo cáo công văn mới nhất
   */
  async getLatestDocumentReport() {
    const query = {
      jobType: CONSTANTS.DOCUMENT_JOB_TYPE_ID,
      deletedAt: { $exists: false },
      status: { $ne: 'draft' },
      'metrics.unansweredDocuments': { $exists: true }
    };

    const latestReport = await Report.findOne(query)
      .sort({ createdAt: -1 })
      .populate('createdBy', 'name idNumber units')
      .populate('unit', 'name')
      .lean();

    return latestReport;
  }

  /**
   * Tính toán thống kê tổng quan văn bản
   * @param {Array} documents - Danh sách văn bản
   * @param {Number} unansweredDocuments - Số công văn chưa trả lời (từ metadata)
   * @returns {Object} Thống kê tổng quan
   */
  calculateDocumentsSummary(documents, unansweredDocuments = 0) {
    const totalReports = documents.length;

    // Tính tổng từ tất cả báo cáo trong khoảng thời gian
    const incomingDocs = documents.map(d => d.metrics?.incomingDocuments || 0).reduce((sum, count) => sum + count, 0);
    const outgoingDocs = documents.map(d => d.metrics?.outgoingDocuments || 0).reduce((sum, count) => sum + count, 0);

    // Không tính replyDocs nữa vì không còn sử dụng
    const totalDocuments = incomingDocs + outgoingDocs;

    return {
      totalReports,
      totalDocuments,
      incomingDocuments: incomingDocs,
      outgoingDocuments: outgoingDocs,
      unansweredDocuments: unansweredDocuments, // Số công văn chưa trả lời từ báo cáo mới nhất
      incomingRate: StatisticsUtils.calculateRate(incomingDocs, totalDocuments),
      outgoingRate: StatisticsUtils.calculateRate(outgoingDocs, totalDocuments),
      unansweredRate: incomingDocs > 0 ? StatisticsUtils.calculateRate(unansweredDocuments, incomingDocs) : 0 // Tỷ lệ chưa trả lời so với tổng công văn đến
    };
  }

  /**
   * Helper function: Tạo vụ việc từ metric của báo cáo (OLD - deprecated)
   * @param {Object} report - Báo cáo gốc
   * @param {String} metricKey - Key của metric
   * @param {Object} metricConfig - Cấu hình metric từ JobType
   * @param {Number} incidentIndex - Số thứ tự vụ việc trong metric
   * @param {String} hasAreaFilter - ID khu vực filter (để xác định hiển thị area nào)
   * @returns {Object} Vụ việc đã format
   */
  createIncidentFromMetric(report, metricKey, metricConfig, incidentIndex, hasAreaFilter) {
    // Tìm thời gian và địa điểm từ details nếu có
    const detailInfo = report.details && report.details[incidentIndex - 1] ? report.details[incidentIndex - 1] : null;
    const incidentTime = detailInfo?.time || report.createdAt;

    return {
      // ID duy nhất cho vụ việc (kết hợp report ID + index)
      _id: `${report._id}_${incidentIndex}`,

      // Thông tin cơ bản
      title: `${report.title || metricConfig.label || metricKey} xảy ra tại ${detailInfo?.location?.address || detailInfo?.location?.areas?.[hasAreaFilter ? detailInfo?.location?.areas.length - 1 : 0]?.name || 'chưa xác định'}`,

      // Thông tin từ báo cáo gốc
      reportId: report._id,

      // Thông tin địa điểm (nếu có)
      location: detailInfo?.location ? {
        address: detailInfo.location.address || '',
        coordinates: detailInfo.location.coordinates || null,
        areas: detailInfo.location.areas || []
      } : null,

      // Thời gian vụ việc
      incidentTime: incidentTime,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,

      // Thời gian hiển thị dễ đọc
      timeAgo: this.getTimeAgo(incidentTime)
    };
  }

  /**
   * Tạo vụ việc từ ReportDetail (NEW - sử dụng cho latest incidents)
   * @param {Object} detail - ReportDetail với areas populated
   * @param {Object} report - Report tương ứng với jobType populated
   * @param {String} hasAreaFilter - ID khu vực filter (để xác định hiển thị area nào)
   * @returns {Object} Vụ việc đã format
   */
  createIncidentFromReportDetail(detail, report, hasAreaFilter) {
    // Xác định area để hiển thị trong title
    let displayAreaName = 'chưa xác định';

    if (detail.areas && detail.areas.length > 0) {
      if (hasAreaFilter) {
        // Nếu có filter area, tìm area con (level cao nhất)
        const filteredAreas = detail.areas.filter(area => area._id.toString() === hasAreaFilter);
        if (filteredAreas.length > 0) {
          displayAreaName = filteredAreas[0].name;
        } else {
          // Nếu không tìm thấy area filter, lấy area có level cao nhất
          const sortedAreas = detail.areas.sort((a, b) => b.level - a.level);
          displayAreaName = sortedAreas[0].name;
        }
      } else {
        // Nếu không có filter, lấy area level 1 (hoặc level thấp nhất)
        const level1Areas = detail.areas.filter(area => area.level === 1);
        if (level1Areas.length > 0) {
          displayAreaName = level1Areas[0].name;
        } else {
          const sortedAreas = detail.areas.sort((a, b) => a.level - b.level);
          displayAreaName = sortedAreas[0].name;
        }
      }
    }

    // Tạo title từ report title hoặc jobType name
    const incidentType = report?.title || report?.jobType?.name || 'Vụ việc';

    return {
      // ID duy nhất cho vụ việc
      _id: detail._id,

      // Thông tin cơ bản
      title: `${incidentType} xảy ra tại ${detail.location?.address || displayAreaName}`,

      // Thông tin từ báo cáo gốc
      reportId: report._id,

      // Thông tin địa điểm từ ReportDetail
      location: {
        address: detail.location?.address || '',
        coordinates: detail.location?.coordinates || null,
        areas: detail.areas || []
      },

      // Thời gian vụ việc từ ReportDetail
      incidentTime: detail.time || report.createdAt,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,

      // Thời gian hiển thị dễ đọc
      timeAgo: this.getTimeAgo(detail.time || report.createdAt)
    };
  }

  /**
   * Helper function: Tính toán thời gian "time ago" dễ đọc
   * @param {Number} timestamp - Timestamp tạo báo cáo
   * @returns {String} Chuỗi mô tả thời gian
   */
  getTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) {
      return 'Vừa xong';
    } else if (minutes < 60) {
      return `${minutes} phút trước`;
    } else if (hours < 24) {
      return `${hours} giờ trước`;
    } else if (days < 7) {
      return `${days} ngày trước`;
    } else {
      return new Date(timestamp).toLocaleDateString('vi-VN');
    }
  }
}

module.exports = new StatisticsService();
